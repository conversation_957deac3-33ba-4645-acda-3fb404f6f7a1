/* PayID Bank Transfer Instructions Styles for Blocks */
.monoova-payid-bank-transfer-instructions-wrapper .monoova-instructions-container {
    text-align: center;
    border: 1px solid #e0e0e0;
    padding: 30px;
    border-radius: 12px;
    /* width: max-content;
    max-width: 480px; */
    /* display: flex; */
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
}

.monoova-payid-bank-transfer-instructions-wrapper .monoova-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    text-align: left;
}

.monoova-payid-bank-transfer-instructions-wrapper .monoova-header h2 {
    font-size: 20px;
    margin: 0;
    color: #333;
}
.monoova-payid-bank-transfer-instructions-wrapper .monoova-instruction-method-selection {
    display: flex;
    align-items: center;
    gap: 30px;
    margin-top: 12px;
}
.monoova-payid-bank-transfer-instructions-wrapper .monoova-instruction-method-selection h4 {
    margin-top: 20px;
    margin-bottom: 20px;
    font-weight: 500;
}
.monoova-payid-bank-transfer-instructions-wrapper .monoova-instruction-method-selection .components-radio-control {
    font-size: var(--wp--preset--font-size--medium);
}
.monoova-payid-bank-transfer-instructions-wrapper .monoova-instruction-method-selection .components-radio-control legend {
    display: none;
}
.monoova-payid-bank-transfer-instructions-wrapper .monoova-instruction-method-selection .components-radio-control .components-radio-control__group-wrapper {
    flex-direction: row !important;
}
.monoova-payid-bank-transfer-instructions-wrapper .monoova-instruction-method-selection .components-radio-control .components-radio-control__group-wrapper .components-radio-control__option label {
    margin-bottom: 0;
}
.monoova-payid-bank-transfer-instructions-wrapper .monoova-instruction-method-selection .components-radio-control .components-radio-control__group-wrapper .components-radio-control__option .components-radio-control__input[type=radio] {
    width: 18px;
    height: 18px;
    max-width: 18px;
    max-height: 18px;
}
.monoova-payid-bank-transfer-instructions-wrapper .monoova-instruction-method-selection .components-radio-control .components-radio-control__group-wrapper .components-radio-control__option .components-radio-control__input[type=radio]:checked {
    background-color: #fff;
    border: 1px solid var(--wp-components-color-accent,var(--wp-admin-theme-color,#3858e9)) !important;
}
.monoova-payid-bank-transfer-instructions-wrapper .monoova-instruction-method-selection .components-radio-control .components-radio-control__group-wrapper .components-radio-control__option .components-radio-control__input[type=radio]:checked:before {
    width: 16px;
    height: 16px;
    background-color: var(--wp-components-color-accent,var(--wp-admin-theme-color,#3858e9)) !important;
}


#monoova-method-switcher {
    border-radius: 8px;
    padding: 8px 12px;
    border: 1px solid #ccc;
}

.monoova-payid-bank-transfer-instructions-wrapper .monoova-instructions-container {
    margin-top: 20px;
}

.monoova-payid-bank-transfer-instructions-wrapper .monoova-instructions-container p {
    color: #555;
    font-size: 14px;
    line-height: 1.5;
}


#monoova-qr-code {
    padding: 10px;
    border: 1px solid #eee;
    border-radius: 8px;
    display: inline-block;
}

#monoova-qr-code img {
    display: block;
}

.monoova-payid-bank-transfer-instructions-wrapper .monoova-instructions-container p {
    color: #555;
    font-size: 14px;
    line-height: 1.5;
}

.monoova-payid-bank-transfer-instructions-wrapper .monoova-scan-pay {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
}

.monoova-payid-bank-transfer-instructions-wrapper .monoova-scan-pay h3 {
    font-size: 24px;
    margin: 0;
    font-weight: bold;
    color: #333;
}

.monoova-payid-bank-transfer-instructions-wrapper .monoova-scan-pay .amount {
    /* font-size: 28px;
    font-weight: 700;
    color: #2CB5C5; */
    margin: 0;
}

.monoova-payid-bank-transfer-instructions-wrapper .monoova-reference-area .ref-label {
    font-size: 14px;
    color: #666;
    margin-right: 8px;
}

.monoova-payid-bank-transfer-instructions-wrapper .monoova-reference-area .ref-value {
    font-weight: bold;
    font-size: 16px;
    letter-spacing: 0.5px;
    color: #2CB5C5;
}

.monoova-payid-bank-transfer-instructions-wrapper .monoova-divider {
    color: #999;
    margin: 18px 0;
    font-size: 13px;
    text-transform: uppercase;
    font-weight: 500;
}

.monoova-payid-bank-transfer-instructions-wrapper .monoova-manual-pay {
    background-color: #ffffff;
    padding: 8px 12px 8px 12px;
    border: 1px solid #E8E8E8;
    border-radius: 8px;
    display: flex;
    max-width: fit-content;
    margin: 0 auto;
    justify-content: space-between;
    align-items: center;
}

.monoova-payid-bank-transfer-instructions-wrapper .monoova-manual-pay .payid-logo {
    height: 20px;
    margin-right: 12px;
}

.monoova-payid-bank-transfer-instructions-wrapper .monoova-manual-pay .copy-target {
    font-family: monospace;
    font-size: 14px;
    color: #333;
}

.monoova-payid-bank-transfer-instructions-wrapper .monoova-copy-button {
    background: #ffffff;
    border: 0;
    border-left: 1px solid #E8E8E8;
    cursor: pointer;
    margin-left: 12px;
    line-height: 1;
    border-radius: 0;
    padding: 0 2px 0 12px;
}

.monoova-payid-bank-transfer-instructions-wrapper .monoova-copy-button:hover {
    background-color: #ffffff;
    border: 0;
    border-left: 1px solid #E8E8E8;
}

.monoova-payid-bank-transfer-instructions-wrapper .monoova-copy-button svg {
    width: 16px;
    height: 16px;
    display: block;
    border: none;
    outline: none;
}

.monoova-payid-bank-transfer-instructions-wrapper .monoova-confirmation-text {
    margin-top: 25px;
    font-size: 13px;
    color: #666;
}

#monoova-expiry-info {
    background-color: #FFEBEB;
    padding: 12px;
    border-radius: 8px;
    margin-top: 20px;
    font-size: 13px;
}

.monoova-payid-bank-transfer-instructions-wrapper .monoova-bank-details {
    text-align: left;
    margin-top: 20px;
}

.monoova-payid-bank-transfer-instructions-wrapper .monoova-bank-details .detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 5px;
    border-bottom: 1px solid #f1f1f1;
}

.monoova-payid-bank-transfer-instructions-wrapper .monoova-bank-details .detail-row:last-child {
    border-bottom: none;
}

.monoova-payid-bank-transfer-instructions-wrapper .monoova-bank-details .detail-label {
    color: #555;
    font-weight: 500;
}

.monoova-payid-bank-transfer-instructions-wrapper .monoova-bank-details .detail-value {
    font-weight: 600;
    font-family: monospace;
}

.monoova-payid-bank-transfer-instructions-wrapper .monoova-bank-details .copy-row .detail-value {
    flex-grow: 1;
    text-align: right;
    margin-right: 10px;
}

#monoova-payid-view .monoova-reference-field {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #fff;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    padding: 10px 15px;
}

/* New styles for Bank Transfer view */
#monoova-bank-view h3 {
    font-size: 18px;
    font-weight: 600;
    margin-top: 0;
    margin-bottom: 20px;
    color: #333;
    text-align: left;
}

.monoova-payid-bank-transfer-instructions-wrapper .monoova-bank-field-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 15px;
}

.monoova-payid-bank-transfer-instructions-wrapper .monoova-bank-field {
    background-color: #f8f9fa;
    border: 1px solid #e8e8e8;
    border-radius: 8px;
    padding: 10px 15px;
}

.monoova-payid-bank-transfer-instructions-wrapper .monoova-bank-field.full-width {
    grid-column: 1 / -1;
}

.monoova-payid-bank-transfer-instructions-wrapper .monoova-bank-field .field-label,
.monoova-payid-bank-transfer-instructions-wrapper .monoova-reference-field .field-label {
    display: block;
    font-size: 12px;
    color: #666;
}

.monoova-payid-bank-transfer-instructions-wrapper .monoova-bank-field .field-value-wrapper,
.monoova-payid-bank-transfer-instructions-wrapper .monoova-reference-field .field-value-wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.monoova-payid-bank-transfer-instructions-wrapper .monoova-bank-field .copy-target,
.monoova-payid-bank-transfer-instructions-wrapper .monoova-reference-field .copy-target {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    font-family: monospace;
}

.monoova-payid-bank-transfer-instructions-wrapper .monoova-bank-field .monoova-copy-button,
.monoova-payid-bank-transfer-instructions-wrapper .monoova-reference-field .monoova-copy-button {
    background: transparent;
    border: none;
    padding: 0;
    margin-left: 10px;
    cursor: pointer;
    color: #2CB5C5;
}

.monoova-payid-bank-transfer-instructions-wrapper .monoova-payment-reference-section {
    margin-top: 25px;
}

.monoova-payid-bank-transfer-instructions-wrapper .monoova-payment-reference-section h4 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 5px;
}

.monoova-payid-bank-transfer-instructions-wrapper .monoova-payment-reference-section p {
    font-size: 14px;
    color: #555;
    margin-top: 0;
    margin-bottom: 10px;
}

.monoova-payid-bank-transfer-instructions-wrapper .monoova-payment-reference-section .monoova-bank-field {
    background-color: #fff;
}

#monoova-bank-view .monoova-bank-field {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.monoova-payid-bank-transfer-instructions-wrapper .monoova-payment-status {
    text-align: center;
}

.monoova-payid-bank-transfer-instructions-wrapper .monoova-payment-status p {
    font-size: 16px;
    color: #484848;
    font-weight: 400;
}

.monoova-payid-bank-transfer-instructions-wrapper .monoova-payment-status .title {
    font-size: 24px;
    font-weight: 600;
}

/* Custom instructions */
.monoova-custom-instructions {
    margin: 15px 0;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
    text-align: left;
}

/* Loading state */
.monoova-payid-loading {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.monoova-payid-loading .spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #2CB5C5;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 15px;
}

.monoova-alert-icon {
    display: inline-block;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    color: #fff;
    text-align: center;
    font-size: 24px;
    line-height: 40px;
    font-weight: bold;
}

.monoova-alert-icon--error {
    background-color: #FF2525;
}

.monoova-alert-icon--warning {
    background-color: #FF782A;
}

.monoova-alert-icon--success {
    background-color: #2CB5C5;
}

/* Payment Status Actions */
.monoova-payment-status-actions .components-button {
    margin-top: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Error state */
.monoova-payid-error {
    text-align: center;
    padding: 20px;
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 6px;
    color: #721c24;
    margin: 15px 0;
}

/* Responsive design */
@media (max-width: 600px) {
    .monoova-payid-bank-transfer-instructions-wrapper .monoova-instructions-container {
        max-width: 100%;
        margin: 20px 10px;
        padding: 20px;
    }

    .monoova-payid-bank-transfer-instructions-wrapper .monoova-scan-pay {
        flex-direction: column;
        gap: 15px;
    }

    .monoova-payid-bank-transfer-instructions-wrapper .monoova-bank-field-grid {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .monoova-payid-bank-transfer-instructions-wrapper .monoova-header {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
}
